import sys
import math
import time
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QSplashScreen, QLabel, QVBoxLayout, QWidget, QProgressBar
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont, QPen, QBrush, QLinearGradient

class AnimatedSplashScreen(QSplashScreen):
    def __init__(self):
        # Create a transparent pixmap
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.transparent)
        
        super().__init__(pixmap, Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        self.counter = 0
        self.progress = 0
        
        # Setup timer for animation
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(50)  # 50ms intervals
        
        # Setup progress timer
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_progress)
        self.progress_timer.start(100)  # Update progress every 100ms
        
        self.start_time = time.time()
        self.duration = 4.0  # 4 seconds
        
    def update_progress(self):
        """Update loading progress"""
        elapsed = time.time() - self.start_time
        self.progress = min(elapsed / self.duration * 100, 100)
        
        if self.progress >= 100:
            self.progress_timer.stop()
            self.timer.stop()
            QTimer.singleShot(500, self.close)  # Close after 500ms
    
    def update_animation(self):
        """Update animation frame"""
        self.counter += 1
        self.update()  # Trigger repaint
    
    def paintEvent(self, event):
        """Custom paint event for animations"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw background with gradient
        gradient = QLinearGradient(0, 0, 0, self.height())
        gradient.setColorAt(0, QColor(20, 20, 40, 200))
        gradient.setColorAt(1, QColor(40, 40, 80, 200))
        painter.fillRect(self.rect(), gradient)
        
        # Draw border
        painter.setPen(QPen(QColor(100, 150, 255, 150), 2))
        painter.drawRect(self.rect().adjusted(1, 1, -1, -1))
        
        self.draw_spinning_elements(painter)
        self.draw_pulsing_logo(painter)
        self.draw_text_and_progress(painter)
    
    def draw_spinning_elements(self, painter):
        """Draw spinning animation elements"""
        center_x, center_y = self.width() // 2, self.height() // 2 - 30
        radius = 60
        
        painter.setPen(Qt.NoPen)
        
        for i in range(8):
            angle = (self.counter * 5 + i * 45) % 360
            x = center_x + radius * math.cos(math.radians(angle))
            y = center_y + radius * math.sin(math.radians(angle))
            
            # Create fading effect
            alpha = int(255 * (i + 1) / 8)
            color = QColor(0, 150, 255, alpha)
            painter.setBrush(QBrush(color))
            
            size = 8 + 4 * math.sin(math.radians(self.counter * 3 + i * 45))
            painter.drawEllipse(int(x - size/2), int(y - size/2), int(size), int(size))
    
    def draw_pulsing_logo(self, painter):
        """Draw pulsing logo in center"""
        center_x, center_y = self.width() // 2, self.height() // 2 - 30
        
        # Pulsing effect
        pulse = math.sin(self.counter * 0.1) * 0.3 + 1.0
        size = int(40 * pulse)
        
        # Draw logo background
        painter.setPen(QPen(QColor(255, 255, 255, 200), 2))
        painter.setBrush(QBrush(QColor(0, 100, 200, 150)))
        painter.drawRoundedRect(
            center_x - size//2, center_y - size//2,
            size, size, 10, 10
        )
        
        # Draw logo text
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont('Arial', 12, QFont.Bold))
        painter.drawText(
            center_x - 15, center_y + 5,
            "APP"
        )
    
    def draw_text_and_progress(self, painter):
        """Draw title, progress bar and status text"""
        # Title
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont('Arial', 16, QFont.Bold))
        painter.drawText(
            20, 40,
            "Loading Application..."
        )
        
        # Progress bar background
        bar_x, bar_y = 50, self.height() - 80
        bar_width, bar_height = self.width() - 100, 20
        
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.setBrush(QBrush(QColor(50, 50, 50, 150)))
        painter.drawRoundedRect(bar_x, bar_y, bar_width, bar_height, 10, 10)
        
        # Progress bar fill
        fill_width = int(bar_width * self.progress / 100)
        if fill_width > 0:
            gradient = QLinearGradient(bar_x, bar_y, bar_x + fill_width, bar_y)
            gradient.setColorAt(0, QColor(0, 150, 255))
            gradient.setColorAt(1, QColor(0, 200, 150))
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(bar_x, bar_y, fill_width, bar_height, 10, 10)
        
        # Status text
        painter.setPen(QColor(200, 200, 200))
        painter.setFont(QFont('Arial', 10))
        
        if self.progress < 30:
            status = "Loading modules..."
        elif self.progress < 60:
            status = "Initializing components..."
        elif self.progress < 90:
            status = "Finalizing setup..."
        else:
            status = "Ready!"
        
        painter.drawText(bar_x, bar_y + 35, status)
        
        # Progress percentage
        painter.drawText(
            bar_x + bar_width - 50, bar_y + 35,
            f"{int(self.progress)}%"
        )

class SimpleAnimatedSplash(QWidget):
    """Simpler version with just transparency and basic animation"""
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(300, 200)
        
        # Center the window
        screen = QApplication.desktop().screenGeometry()
        self.move((screen.width() - self.width()) // 2,
                 (screen.height() - self.height()) // 2)
        
        self.counter = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.animate)
        self.timer.start(50)
        
        # Auto close after 3 seconds
        QTimer.singleShot(3000, self.close)
    
    def animate(self):
        self.counter += 1
        self.update()
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Semi-transparent background
        painter.fillRect(self.rect(), QColor(0, 0, 0, 100))
        
        # Animated circle
        center_x, center_y = self.width() // 2, self.height() // 2
        radius = 30 + 10 * math.sin(self.counter * 0.1)
        
        painter.setPen(QPen(QColor(0, 150, 255, 200), 3))
        painter.setBrush(Qt.NoBrush)
        painter.drawEllipse(
            int(center_x - radius), int(center_y - radius),
            int(radius * 2), int(radius * 2)
        )
        
        # Text
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont('Arial', 14, QFont.Bold))
        painter.drawText(self.rect(), Qt.AlignCenter, "Loading...")

def show_splash_screen():
    """Show the splash screen"""
    app = QApplication(sys.argv)
    
    # Choose which splash screen to use
    # splash = AnimatedSplashScreen()  # Full featured
    splash = SimpleAnimatedSplash()    # Simple version
    
    splash.show()
    
    return app.exec_()

if __name__ == "__main__":
    show_splash_screen()
