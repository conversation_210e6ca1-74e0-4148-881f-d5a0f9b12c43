import tkinter as tk
from tkinter import ttk
import math
import time
from PIL import Image, ImageTk, ImageDraw
import threading

class AnimatedSplashScreen:
    def __init__(self, duration=3000):
        self.root = tk.Tk()
        self.duration = duration  # Duration in milliseconds
        self.setup_window()
        self.create_animated_elements()
        self.start_time = time.time()
        
    def setup_window(self):
        """Setup the splash screen window with transparency"""
        # Remove window decorations
        self.root.overrideredirect(True)
        
        # Set window size and center it
        width, height = 400, 300
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        # Set transparency and background
        self.root.configure(bg='black')
        self.root.attributes('-alpha', 0.9)  # Semi-transparent
        self.root.attributes('-topmost', True)  # Always on top
        
        # Create main frame
        self.main_frame = tk.Frame(self.root, bg='black')
        self.main_frame.pack(fill='both', expand=True)
        
    def create_animated_elements(self):
        """Create animated elements for the splash screen"""
        # Title label
        self.title_label = tk.Label(
            self.main_frame,
            text="Loading Application...",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg='black'
        )
        self.title_label.pack(pady=30)
        
        # Canvas for animated graphics
        self.canvas = tk.Canvas(
            self.main_frame,
            width=300,
            height=150,
            bg='black',
            highlightthickness=0
        )
        self.canvas.pack(pady=20)
        
        # Progress bar
        self.progress = ttk.Progressbar(
            self.main_frame,
            length=250,
            mode='determinate'
        )
        self.progress.pack(pady=20)
        
        # Status label
        self.status_label = tk.Label(
            self.main_frame,
            text="Initializing...",
            font=('Arial', 10),
            fg='lightgray',
            bg='black'
        )
        self.status_label.pack()
        
    def create_spinning_circle(self, frame):
        """Create animated spinning circle"""
        self.canvas.delete("spinner")
        
        center_x, center_y = 150, 75
        radius = 30
        
        # Create multiple circles for spinning effect
        for i in range(8):
            angle = (frame * 10 + i * 45) % 360
            x = center_x + radius * math.cos(math.radians(angle))
            y = center_y + radius * math.sin(math.radians(angle))
            
            # Fade effect based on position
            alpha = int(255 * (i + 1) / 8)
            color = f"#{alpha:02x}{alpha:02x}{255:02x}"
            
            self.canvas.create_oval(
                x-5, y-5, x+5, y+5,
                fill=color,
                outline="",
                tags="spinner"
            )
    
    def create_pulsing_logo(self, frame):
        """Create pulsing logo effect"""
        self.canvas.delete("logo")
        
        center_x, center_y = 150, 75
        base_size = 40
        pulse = math.sin(frame * 0.2) * 10
        size = base_size + pulse
        
        # Create pulsing rectangle (placeholder for logo)
        self.canvas.create_rectangle(
            center_x - size//2, center_y - size//2,
            center_x + size//2, center_y + size//2,
            fill="cyan",
            outline="white",
            width=2,
            tags="logo"
        )
        
        # Add text in the center
        self.canvas.create_text(
            center_x, center_y,
            text="APP",
            font=('Arial', 12, 'bold'),
            fill="black",
            tags="logo"
        )
    
    def update_animation(self, frame=0):
        """Update animation frame"""
        # Calculate progress
        elapsed = time.time() - self.start_time
        progress = min((elapsed * 1000) / self.duration * 100, 100)
        
        # Update progress bar
        self.progress['value'] = progress
        
        # Update status text
        if progress < 30:
            self.status_label.config(text="Loading modules...")
        elif progress < 60:
            self.status_label.config(text="Initializing components...")
        elif progress < 90:
            self.status_label.config(text="Finalizing setup...")
        else:
            self.status_label.config(text="Ready!")
        
        # Create animations
        self.create_spinning_circle(frame)
        self.create_pulsing_logo(frame)
        
        # Continue animation or close
        if progress < 100:
            self.root.after(50, lambda: self.update_animation(frame + 1))
        else:
            self.root.after(1000, self.close)  # Wait 1 second before closing
    
    def close(self):
        """Close the splash screen"""
        self.root.destroy()
    
    def show(self):
        """Show the splash screen"""
        self.update_animation()
        self.root.mainloop()

def create_transparent_gif():
    """Create a transparent animated GIF"""
    frames = []
    
    for i in range(20):
        # Create image with transparency
        img = Image.new('RGBA', (200, 200), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Animate a rotating circle
        angle = i * 18  # 18 degrees per frame
        center = (100, 100)
        radius = 50
        
        # Draw rotating elements
        for j in range(6):
            element_angle = angle + j * 60
            x = center[0] + radius * math.cos(math.radians(element_angle))
            y = center[1] + radius * math.sin(math.radians(element_angle))
            
            # Fade effect
            alpha = int(255 * (j + 1) / 6)
            color = (0, 150, 255, alpha)
            
            draw.ellipse([x-10, y-10, x+10, y+10], fill=color)
        
        frames.append(img)
    
    # Save as animated GIF
    frames[0].save(
        'animated_logo.gif',
        save_all=True,
        append_images=frames[1:],
        duration=100,
        loop=0,
        transparency=0,
        disposal=2
    )
    print("Transparent animated GIF created: animated_logo.gif")

if __name__ == "__main__":
    # Create animated GIF
    create_transparent_gif()
    
    # Show splash screen
    splash = AnimatedSplashScreen(duration=4000)  # 4 seconds
    splash.show()
