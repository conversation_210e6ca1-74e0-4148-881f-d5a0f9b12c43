#!/usr/bin/env python3
"""
Blood Drip Demo - Quick showcase of different blood effects
"""

import tkinter as tk
from blood_splash_screen import show_blood_splash, BLOOD_VARIATIONS
import time

class BloodDemo:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🩸 Blood Drip Effects Demo 🩸")
        self.root.geometry("500x600")
        self.root.configure(bg='black')
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup demo UI"""
        # Title
        title_label = tk.Label(
            self.root,
            text="🩸 BLOOD DRIP EFFECTS 🩸",
            font=('Arial', 20, 'bold'),
            fg='darkred',
            bg='black'
        )
        title_label.pack(pady=20)
        
        # Subtitle
        subtitle_label = tk.Label(
            self.root,
            text="Dramatic Splash Screen Animations",
            font=('Arial', 12),
            fg='lightcoral',
            bg='black'
        )
        subtitle_label.pack(pady=5)
        
        # Description
        desc_label = tk.Label(
            self.root,
            text="Choose a blood drip animation style for your application splash screen.\nEach effect creates a small, centered, transparent window that drips blood once.",
            font=('Arial', 10),
            fg='white',
            bg='black',
            wraplength=450,
            justify='center'
        )
        desc_label.pack(pady=15)
        
        # Buttons frame
        buttons_frame = tk.Frame(self.root, bg='black')
        buttons_frame.pack(pady=20, fill='both', expand=True)
        
        # Create buttons for each variation
        variations_info = [
            ('classic', '🩸 Classic Drip', 'Single blood drop with trailing effect'),
            ('multi_drip', '🩸🩸 Multi Drip', 'Multiple drops falling in sequence'),
            ('vampire_fangs', '🧛 Vampire Fangs', 'Blood dripping from vampire fangs'),
            ('quick_drip', '⚡ Quick Drip', 'Fast 2-second blood drop'),
            ('slow_dramatic', '🎭 Slow Dramatic', 'Slow 5-second dramatic effect')
        ]
        
        for i, (key, title, desc) in enumerate(variations_info):
            # Button frame
            btn_frame = tk.Frame(buttons_frame, bg='#1a1a1a', relief='raised', bd=2)
            btn_frame.pack(fill='x', padx=20, pady=8)
            
            # Main button
            btn = tk.Button(
                btn_frame,
                text=title,
                command=lambda k=key: self.show_effect(k),
                font=('Arial', 14, 'bold'),
                bg='darkred',
                fg='white',
                activebackground='red',
                activeforeground='white',
                padx=20,
                pady=10,
                cursor='hand2'
            )
            btn.pack(side='left', padx=10, pady=5)
            
            # Description
            desc_lbl = tk.Label(
                btn_frame,
                text=desc,
                font=('Arial', 9),
                fg='lightgray',
                bg='#1a1a1a'
            )
            desc_lbl.pack(side='left', padx=10, pady=5)
        
        # Special effects section
        special_frame = tk.Frame(self.root, bg='black')
        special_frame.pack(pady=20, fill='x')
        
        tk.Label(
            special_frame,
            text="🎨 Special Effects",
            font=('Arial', 16, 'bold'),
            fg='purple',
            bg='black'
        ).pack()
        
        # Special buttons
        special_btn_frame = tk.Frame(special_frame, bg='black')
        special_btn_frame.pack(pady=10)
        
        tk.Button(
            special_btn_frame,
            text="🎲 Random Effect",
            command=self.random_effect,
            font=('Arial', 12),
            bg='purple',
            fg='white',
            padx=15,
            pady=5
        ).pack(side='left', padx=5)
        
        tk.Button(
            special_btn_frame,
            text="🔄 Show All (Sequence)",
            command=self.show_all_sequence,
            font=('Arial', 12),
            bg='orange',
            fg='white',
            padx=15,
            pady=5
        ).pack(side='left', padx=5)
        
        tk.Button(
            special_btn_frame,
            text="📱 Open Full Picker",
            command=self.open_picker,
            font=('Arial', 12),
            bg='blue',
            fg='white',
            padx=15,
            pady=5
        ).pack(side='left', padx=5)
        
        # Status label
        self.status_label = tk.Label(
            self.root,
            text="Ready - Click any effect to preview",
            font=('Arial', 10),
            fg='gray',
            bg='black'
        )
        self.status_label.pack(side='bottom', pady=10)
        
        # Instructions
        instructions = tk.Label(
            self.root,
            text="💡 Tip: Each effect opens a small centered window that closes automatically",
            font=('Arial', 9),
            fg='yellow',
            bg='black'
        )
        instructions.pack(side='bottom', pady=5)
    
    def show_effect(self, variation_key):
        """Show a specific blood effect"""
        self.status_label.config(text=f"Showing {variation_key.replace('_', ' ').title()} effect...")
        self.root.update()
        
        try:
            show_blood_splash(variation_key)
            self.status_label.config(text=f"✅ {variation_key.replace('_', ' ').title()} effect completed")
        except Exception as e:
            self.status_label.config(text=f"❌ Error: {str(e)}")
    
    def random_effect(self):
        """Show a random blood effect"""
        import random
        variation = random.choice(list(BLOOD_VARIATIONS.keys()))
        self.show_effect(variation)
    
    def show_all_sequence(self):
        """Show all effects in sequence"""
        self.status_label.config(text="🎬 Playing all effects in sequence...")
        self.root.update()
        
        def show_next(variations, index=0):
            if index < len(variations):
                variation = variations[index]
                self.status_label.config(text=f"Playing {index+1}/{len(variations)}: {variation}")
                self.root.update()
                
                try:
                    show_blood_splash(variation)
                    # Schedule next effect
                    self.root.after(1000, lambda: show_next(variations, index + 1))
                except Exception as e:
                    self.status_label.config(text=f"❌ Error in sequence: {str(e)}")
            else:
                self.status_label.config(text="🎉 All effects sequence completed!")
        
        variations = list(BLOOD_VARIATIONS.keys())
        show_next(variations)
    
    def open_picker(self):
        """Open the full picker application"""
        try:
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "blood_drip_variations.py"])
            self.status_label.config(text="📱 Full picker application launched")
        except Exception as e:
            self.status_label.config(text=f"❌ Error opening picker: {str(e)}")
    
    def run(self):
        """Run the demo"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🩸 Starting Blood Drip Effects Demo...")
    demo = BloodDemo()
    demo.run()
