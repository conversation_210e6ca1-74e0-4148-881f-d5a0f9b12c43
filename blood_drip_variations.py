import tkinter as tk
from tkinter import ttk
import math
import random
import time
from PIL import Image, ImageDraw, ImageTk

class BloodDripVariations:
    def __init__(self):
        self.variations = []
        self.create_all_variations()
    
    def create_all_variations(self):
        """Create 50 different blood drip variations"""
        
        # Variation 1-5: Classic single drip with different speeds
        for i in range(5):
            self.variations.append({
                'name': f'Classic Drip Speed {i+1}',
                'type': 'classic_drip',
                'speed': 2 + i * 2,
                'color': '#8B0000',
                'size': 8,
                'trail': True
            })
        
        # Variation 6-10: Different blood colors
        colors = ['#8B0000', '#A0001C', '#660000', '#B22222', '#DC143C']
        for i, color in enumerate(colors):
            self.variations.append({
                'name': f'Blood Color {i+1}',
                'type': 'classic_drip',
                'speed': 4,
                'color': color,
                'size': 10,
                'trail': True
            })
        
        # Variation 11-15: Different drip sizes
        for i in range(5):
            self.variations.append({
                'name': f'Drip Size {i+1}',
                'type': 'classic_drip',
                'speed': 4,
                'color': '#8B0000',
                'size': 4 + i * 4,
                'trail': True
            })
        
        # Variation 16-20: Multiple drips
        for i in range(5):
            self.variations.append({
                'name': f'Multi Drip {i+1}',
                'type': 'multi_drip',
                'speed': 3,
                'color': '#8B0000',
                'count': i + 2,
                'size': 6
            })
        
        # Variation 21-25: Splatter effects
        for i in range(5):
            self.variations.append({
                'name': f'Splatter {i+1}',
                'type': 'splatter',
                'speed': 5,
                'color': '#8B0000',
                'intensity': i + 1,
                'size': 8
            })
        
        # Variation 26-30: Dripping text/letters
        letters = ['V', 'X', 'A', 'Z', '♦']
        for i, letter in enumerate(letters):
            self.variations.append({
                'name': f'Dripping {letter}',
                'type': 'dripping_text',
                'speed': 3,
                'color': '#8B0000',
                'text': letter,
                'size': 40
            })
        
        # Variation 31-35: Pulsing blood drops
        for i in range(5):
            self.variations.append({
                'name': f'Pulsing Drop {i+1}',
                'type': 'pulsing_drop',
                'speed': 2 + i,
                'color': '#8B0000',
                'pulse_rate': 0.1 + i * 0.05,
                'size': 12
            })
        
        # Variation 36-40: Flowing blood streams
        for i in range(5):
            self.variations.append({
                'name': f'Blood Stream {i+1}',
                'type': 'blood_stream',
                'speed': 2,
                'color': '#8B0000',
                'width': 2 + i * 2,
                'length': 50 + i * 20
            })
        
        # Variation 41-45: Animated blood pools
        for i in range(5):
            self.variations.append({
                'name': f'Blood Pool {i+1}',
                'type': 'blood_pool',
                'speed': 1,
                'color': '#8B0000',
                'spread_rate': 2 + i,
                'max_size': 30 + i * 10
            })
        
        # Variation 46-50: Special effects
        special_effects = [
            {'name': 'Glowing Blood', 'type': 'glowing_drip', 'glow': True},
            {'name': 'Dripping Fang', 'type': 'fang_drip', 'fang': True},
            {'name': 'Blood Rain', 'type': 'blood_rain', 'count': 10},
            {'name': 'Spiral Drip', 'type': 'spiral_drip', 'spiral': True},
            {'name': 'Explosive Drop', 'type': 'explosive_drop', 'explosion': True}
        ]
        
        for effect in special_effects:
            variation = {
                'speed': 3,
                'color': '#8B0000',
                'size': 10
            }
            variation.update(effect)
            self.variations.append(variation)

class BloodDripAnimation:
    def __init__(self, canvas, variation, width=300, height=400):
        self.canvas = canvas
        self.variation = variation
        self.width = width
        self.height = height
        self.frame = 0
        self.drops = []
        self.pools = []
        self.reset_animation()
    
    def reset_animation(self):
        """Reset animation to beginning"""
        self.frame = 0
        self.drops = []
        self.pools = []
        self.canvas.delete("all")
        
        # Initialize based on variation type
        if self.variation['type'] == 'multi_drip':
            for i in range(self.variation['count']):
                self.drops.append({
                    'x': self.width // 2 + (i - self.variation['count']//2) * 20,
                    'y': 50,
                    'speed': self.variation['speed'] + random.uniform(-1, 1),
                    'size': self.variation['size'] + random.randint(-2, 2)
                })
        elif self.variation['type'] == 'blood_rain':
            for i in range(self.variation['count']):
                self.drops.append({
                    'x': random.randint(50, self.width - 50),
                    'y': random.randint(20, 60),
                    'speed': random.uniform(2, 6),
                    'size': random.randint(3, 8)
                })
        else:
            self.drops.append({
                'x': self.width // 2,
                'y': 50,
                'speed': self.variation['speed'],
                'size': self.variation['size']
            })
    
    def animate_frame(self):
        """Animate one frame"""
        self.canvas.delete("all")
        self.frame += 1
        
        # Draw background
        self.canvas.create_rectangle(0, 0, self.width, self.height, fill='black', outline='')
        
        # Animate based on type
        if self.variation['type'] == 'classic_drip':
            self.animate_classic_drip()
        elif self.variation['type'] == 'multi_drip':
            self.animate_multi_drip()
        elif self.variation['type'] == 'splatter':
            self.animate_splatter()
        elif self.variation['type'] == 'dripping_text':
            self.animate_dripping_text()
        elif self.variation['type'] == 'pulsing_drop':
            self.animate_pulsing_drop()
        elif self.variation['type'] == 'blood_stream':
            self.animate_blood_stream()
        elif self.variation['type'] == 'blood_pool':
            self.animate_blood_pool()
        elif self.variation['type'] == 'glowing_drip':
            self.animate_glowing_drip()
        elif self.variation['type'] == 'fang_drip':
            self.animate_fang_drip()
        elif self.variation['type'] == 'blood_rain':
            self.animate_blood_rain()
        elif self.variation['type'] == 'spiral_drip':
            self.animate_spiral_drip()
        elif self.variation['type'] == 'explosive_drop':
            self.animate_explosive_drop()
        
        # Reset if animation is complete
        if self.frame > 200:  # Reset after ~4 seconds at 50fps
            self.reset_animation()
    
    def animate_classic_drip(self):
        """Classic single blood drip"""
        drop = self.drops[0]
        
        # Draw trail if enabled
        if self.variation.get('trail', False):
            for i in range(5):
                trail_y = drop['y'] - i * 10
                if trail_y > 50:
                    alpha = 255 - i * 50
                    trail_color = self.blend_color(self.variation['color'], alpha)
                    self.canvas.create_oval(
                        drop['x'] - 2, trail_y - 2,
                        drop['x'] + 2, trail_y + 2,
                        fill=trail_color, outline=''
                    )
        
        # Draw main drop
        self.canvas.create_oval(
            drop['x'] - drop['size']//2, drop['y'] - drop['size']//2,
            drop['x'] + drop['size']//2, drop['y'] + drop['size']//2,
            fill=self.variation['color'], outline=''
        )
        
        # Update position
        drop['y'] += drop['speed']
        
        # Create splash when hitting bottom
        if drop['y'] > self.height - 50:
            self.create_splash(drop['x'], self.height - 50)
    
    def animate_multi_drip(self):
        """Multiple blood drips"""
        for drop in self.drops:
            self.canvas.create_oval(
                drop['x'] - drop['size']//2, drop['y'] - drop['size']//2,
                drop['x'] + drop['size']//2, drop['y'] + drop['size']//2,
                fill=self.variation['color'], outline=''
            )
            
            drop['y'] += drop['speed']
            
            if drop['y'] > self.height - 50:
                self.create_splash(drop['x'], self.height - 50)
    
    def animate_splatter(self):
        """Blood splatter effect"""
        drop = self.drops[0]
        
        # Main drop
        self.canvas.create_oval(
            drop['x'] - drop['size']//2, drop['y'] - drop['size']//2,
            drop['x'] + drop['size']//2, drop['y'] + drop['size']//2,
            fill=self.variation['color'], outline=''
        )
        
        # Splatter particles
        if drop['y'] > self.height // 2:
            for i in range(self.variation['intensity'] * 3):
                angle = random.uniform(0, 2 * math.pi)
                distance = random.uniform(10, 30)
                splash_x = drop['x'] + distance * math.cos(angle)
                splash_y = drop['y'] + distance * math.sin(angle)
                
                self.canvas.create_oval(
                    splash_x - 2, splash_y - 2,
                    splash_x + 2, splash_y + 2,
                    fill=self.variation['color'], outline=''
                )
        
        drop['y'] += drop['speed']
    
    def animate_dripping_text(self):
        """Dripping text effect"""
        # Draw the main text
        self.canvas.create_text(
            self.width // 2, 80,
            text=self.variation['text'],
            font=('Arial', self.variation['size'], 'bold'),
            fill=self.variation['color']
        )
        
        # Dripping effect from text
        drop = self.drops[0]
        for i in range(3):
            drip_x = self.width // 2 + (i - 1) * 15
            drip_y = 110 + i * 20 + self.frame * 2
            
            if drip_y < self.height - 50:
                size = 6 - i
                self.canvas.create_oval(
                    drip_x - size, drip_y - size,
                    drip_x + size, drip_y + size,
                    fill=self.variation['color'], outline=''
                )
    
    def animate_pulsing_drop(self):
        """Pulsing blood drop"""
        drop = self.drops[0]
        
        # Pulsing size
        pulse = math.sin(self.frame * self.variation['pulse_rate']) * 0.3 + 1.0
        current_size = int(drop['size'] * pulse)
        
        self.canvas.create_oval(
            drop['x'] - current_size//2, drop['y'] - current_size//2,
            drop['x'] + current_size//2, drop['y'] + current_size//2,
            fill=self.variation['color'], outline=''
        )
        
        drop['y'] += drop['speed']
    
    def animate_blood_stream(self):
        """Flowing blood stream"""
        stream_x = self.width // 2
        stream_width = self.variation['width']
        
        for i in range(0, self.variation['length'], 5):
            y_pos = 50 + i + self.frame * 2
            if y_pos < self.height - 50:
                # Add slight wave motion
                wave_offset = math.sin((y_pos + self.frame) * 0.1) * 5
                
                self.canvas.create_rectangle(
                    stream_x - stream_width//2 + wave_offset,
                    y_pos,
                    stream_x + stream_width//2 + wave_offset,
                    y_pos + 5,
                    fill=self.variation['color'], outline=''
                )
    
    def animate_blood_pool(self):
        """Expanding blood pool"""
        if not self.pools:
            self.pools.append({
                'x': self.width // 2,
                'y': self.height - 100,
                'size': 0
            })
        
        pool = self.pools[0]
        
        # Expand pool
        if pool['size'] < self.variation['max_size']:
            pool['size'] += self.variation['spread_rate']
        
        # Draw pool
        self.canvas.create_oval(
            pool['x'] - pool['size'], pool['y'] - pool['size']//3,
            pool['x'] + pool['size'], pool['y'] + pool['size']//3,
            fill=self.variation['color'], outline=''
        )
        
        # Drip feeding the pool
        drop_y = 50 + self.frame * 3
        if drop_y < pool['y']:
            self.canvas.create_oval(
                pool['x'] - 4, drop_y - 4,
                pool['x'] + 4, drop_y + 4,
                fill=self.variation['color'], outline=''
            )
    
    def animate_glowing_drip(self):
        """Glowing blood drip"""
        drop = self.drops[0]
        
        # Draw glow effect (multiple circles with decreasing opacity)
        for i in range(5):
            glow_size = drop['size'] + i * 3
            alpha = 100 - i * 20
            glow_color = self.blend_color('#FF0000', alpha)
            
            self.canvas.create_oval(
                drop['x'] - glow_size//2, drop['y'] - glow_size//2,
                drop['x'] + glow_size//2, drop['y'] + glow_size//2,
                fill=glow_color, outline=''
            )
        
        # Main drop
        self.canvas.create_oval(
            drop['x'] - drop['size']//2, drop['y'] - drop['size']//2,
            drop['x'] + drop['size']//2, drop['y'] + drop['size']//2,
            fill=self.variation['color'], outline=''
        )
        
        drop['y'] += drop['speed']
    
    def animate_fang_drip(self):
        """Blood dripping from fangs"""
        # Draw fangs
        fang1_x, fang2_x = self.width//2 - 20, self.width//2 + 20
        fang_y = 60
        
        # Draw fang shapes
        self.canvas.create_polygon(
            fang1_x, fang_y, fang1_x - 8, fang_y + 25, fang1_x + 8, fang_y + 25,
            fill='white', outline='gray'
        )
        self.canvas.create_polygon(
            fang2_x, fang_y, fang2_x - 8, fang_y + 25, fang2_x + 8, fang_y + 25,
            fill='white', outline='gray'
        )
        
        # Blood dripping from fang tips
        for fang_x in [fang1_x, fang2_x]:
            drip_y = fang_y + 25 + self.frame * 2
            if drip_y < self.height - 50:
                self.canvas.create_oval(
                    fang_x - 3, drip_y - 6,
                    fang_x + 3, drip_y + 6,
                    fill=self.variation['color'], outline=''
                )
    
    def animate_blood_rain(self):
        """Multiple blood drops falling like rain"""
        for drop in self.drops:
            self.canvas.create_oval(
                drop['x'] - drop['size']//2, drop['y'] - drop['size']//2,
                drop['x'] + drop['size']//2, drop['y'] + drop['size']//2,
                fill=self.variation['color'], outline=''
            )
            
            drop['y'] += drop['speed']
            
            # Reset drop when it hits bottom
            if drop['y'] > self.height:
                drop['y'] = random.randint(-50, 0)
                drop['x'] = random.randint(50, self.width - 50)
    
    def animate_spiral_drip(self):
        """Spiral blood drip"""
        drop = self.drops[0]
        
        # Spiral motion
        spiral_radius = 30
        spiral_x = self.width // 2 + spiral_radius * math.cos(self.frame * 0.2)
        spiral_y = 50 + self.frame * 2
        
        self.canvas.create_oval(
            spiral_x - drop['size']//2, spiral_y - drop['size']//2,
            spiral_x + drop['size']//2, spiral_y + drop['size']//2,
            fill=self.variation['color'], outline=''
        )
        
        # Trail
        for i in range(10):
            trail_frame = self.frame - i * 2
            if trail_frame > 0:
                trail_x = self.width // 2 + spiral_radius * math.cos(trail_frame * 0.2)
                trail_y = 50 + trail_frame * 2
                alpha = 255 - i * 25
                trail_color = self.blend_color(self.variation['color'], alpha)
                
                self.canvas.create_oval(
                    trail_x - 2, trail_y - 2,
                    trail_x + 2, trail_y + 2,
                    fill=trail_color, outline=''
                )
    
    def animate_explosive_drop(self):
        """Explosive blood drop"""
        drop = self.drops[0]
        
        if drop['y'] < self.height // 2:
            # Normal drop
            self.canvas.create_oval(
                drop['x'] - drop['size']//2, drop['y'] - drop['size']//2,
                drop['x'] + drop['size']//2, drop['y'] + drop['size']//2,
                fill=self.variation['color'], outline=''
            )
            drop['y'] += drop['speed']
        else:
            # Explosion effect
            explosion_radius = (self.frame - 100) * 3
            if explosion_radius > 0:
                for i in range(20):
                    angle = i * (2 * math.pi / 20)
                    particle_x = drop['x'] + explosion_radius * math.cos(angle)
                    particle_y = drop['y'] + explosion_radius * math.sin(angle)
                    
                    self.canvas.create_oval(
                        particle_x - 3, particle_y - 3,
                        particle_x + 3, particle_y + 3,
                        fill=self.variation['color'], outline=''
                    )
    
    def create_splash(self, x, y):
        """Create splash effect"""
        for i in range(8):
            angle = i * (2 * math.pi / 8)
            splash_x = x + 15 * math.cos(angle)
            splash_y = y + 8 * math.sin(angle)
            
            self.canvas.create_oval(
                splash_x - 2, splash_y - 2,
                splash_x + 2, splash_y + 2,
                fill=self.variation['color'], outline=''
            )
    
    def blend_color(self, color, alpha):
        """Blend color with alpha for transparency effect"""
        # Simple color blending - in a real app you'd use proper alpha blending
        if alpha < 100:
            return '#440000'  # Darker version
        elif alpha < 200:
            return '#660000'  # Medium version
        else:
            return color      # Full color

class BloodDripPicker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Blood Drip Animation Picker - 50 Variations")
        self.root.geometry("800x600")
        self.root.configure(bg='black')

        self.variations = BloodDripVariations().variations
        self.current_index = 0
        self.animation = None
        self.is_playing = False

        self.setup_ui()
        self.load_current_variation()

    def setup_ui(self):
        """Setup the picker UI"""
        # Title
        title_label = tk.Label(
            self.root,
            text="🩸 Blood Drip Animation Picker 🩸",
            font=('Arial', 18, 'bold'),
            fg='darkred',
            bg='black'
        )
        title_label.pack(pady=10)

        # Main frame
        main_frame = tk.Frame(self.root, bg='black')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Left panel - Animation display
        left_frame = tk.Frame(main_frame, bg='black')
        left_frame.pack(side='left', fill='both', expand=True)

        # Animation canvas
        self.canvas = tk.Canvas(
            left_frame,
            width=300,
            height=400,
            bg='black',
            highlightthickness=2,
            highlightbackground='darkred'
        )
        self.canvas.pack(pady=10)

        # Animation controls
        control_frame = tk.Frame(left_frame, bg='black')
        control_frame.pack(pady=10)

        self.play_button = tk.Button(
            control_frame,
            text="▶ Play",
            command=self.toggle_animation,
            font=('Arial', 12, 'bold'),
            bg='darkred',
            fg='white',
            padx=20
        )
        self.play_button.pack(side='left', padx=5)

        tk.Button(
            control_frame,
            text="🔄 Reset",
            command=self.reset_animation,
            font=('Arial', 12),
            bg='gray',
            fg='white',
            padx=15
        ).pack(side='left', padx=5)

        # Right panel - Variation selector
        right_frame = tk.Frame(main_frame, bg='black', width=250)
        right_frame.pack(side='right', fill='y', padx=(20, 0))
        right_frame.pack_propagate(False)

        # Current variation info
        info_frame = tk.Frame(right_frame, bg='black')
        info_frame.pack(fill='x', pady=10)

        tk.Label(
            info_frame,
            text="Current Variation:",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='black'
        ).pack()

        self.current_label = tk.Label(
            info_frame,
            text="",
            font=('Arial', 11),
            fg='lightcoral',
            bg='black',
            wraplength=200
        )
        self.current_label.pack(pady=5)

        self.index_label = tk.Label(
            info_frame,
            text="",
            font=('Arial', 10),
            fg='gray',
            bg='black'
        )
        self.index_label.pack()

        # Navigation buttons
        nav_frame = tk.Frame(right_frame, bg='black')
        nav_frame.pack(fill='x', pady=10)

        tk.Button(
            nav_frame,
            text="⬅ Previous",
            command=self.previous_variation,
            font=('Arial', 10),
            bg='darkred',
            fg='white'
        ).pack(side='left', padx=2)

        tk.Button(
            nav_frame,
            text="Next ➡",
            command=self.next_variation,
            font=('Arial', 10),
            bg='darkred',
            fg='white'
        ).pack(side='right', padx=2)

        # Variation list
        tk.Label(
            right_frame,
            text="All Variations:",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='black'
        ).pack(pady=(20, 5))

        # Scrollable listbox
        list_frame = tk.Frame(right_frame, bg='black')
        list_frame.pack(fill='both', expand=True)

        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side='right', fill='y')

        self.variation_listbox = tk.Listbox(
            list_frame,
            yscrollcommand=scrollbar.set,
            font=('Arial', 9),
            bg='#1a1a1a',
            fg='lightcoral',
            selectbackground='darkred',
            selectforeground='white',
            height=15
        )
        self.variation_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.variation_listbox.yview)

        # Populate listbox
        for i, var in enumerate(self.variations):
            self.variation_listbox.insert(tk.END, f"{i+1:2d}. {var['name']}")

        self.variation_listbox.bind('<<ListboxSelect>>', self.on_listbox_select)

        # Bottom buttons
        bottom_frame = tk.Frame(right_frame, bg='black')
        bottom_frame.pack(fill='x', pady=10)

        tk.Button(
            bottom_frame,
            text="🎲 Random",
            command=self.random_variation,
            font=('Arial', 10),
            bg='purple',
            fg='white'
        ).pack(side='left', padx=2)

        tk.Button(
            bottom_frame,
            text="💾 Export",
            command=self.export_variation,
            font=('Arial', 10),
            bg='green',
            fg='white'
        ).pack(side='right', padx=2)

    def load_current_variation(self):
        """Load the current variation"""
        variation = self.variations[self.current_index]
        self.animation = BloodDripAnimation(self.canvas, variation)

        # Update UI
        self.current_label.config(text=variation['name'])
        self.index_label.config(text=f"#{self.current_index + 1} of {len(self.variations)}")

        # Update listbox selection
        self.variation_listbox.selection_clear(0, tk.END)
        self.variation_listbox.selection_set(self.current_index)
        self.variation_listbox.see(self.current_index)

        # Reset animation
        self.animation.reset_animation()

    def toggle_animation(self):
        """Toggle animation play/pause"""
        if self.is_playing:
            self.is_playing = False
            self.play_button.config(text="▶ Play")
        else:
            self.is_playing = True
            self.play_button.config(text="⏸ Pause")
            self.animate()

    def animate(self):
        """Animation loop"""
        if self.is_playing and self.animation:
            self.animation.animate_frame()
            self.root.after(50, self.animate)  # ~20 FPS

    def reset_animation(self):
        """Reset current animation"""
        if self.animation:
            self.animation.reset_animation()

    def previous_variation(self):
        """Go to previous variation"""
        self.current_index = (self.current_index - 1) % len(self.variations)
        self.load_current_variation()

    def next_variation(self):
        """Go to next variation"""
        self.current_index = (self.current_index + 1) % len(self.variations)
        self.load_current_variation()

    def random_variation(self):
        """Go to random variation"""
        self.current_index = random.randint(0, len(self.variations) - 1)
        self.load_current_variation()

    def on_listbox_select(self, event):
        """Handle listbox selection"""
        selection = self.variation_listbox.curselection()
        if selection:
            self.current_index = selection[0]
            self.load_current_variation()

    def export_variation(self):
        """Export current variation code"""
        variation = self.variations[self.current_index]

        # Create export window
        export_window = tk.Toplevel(self.root)
        export_window.title(f"Export: {variation['name']}")
        export_window.geometry("500x400")
        export_window.configure(bg='black')

        tk.Label(
            export_window,
            text=f"Code for: {variation['name']}",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg='black'
        ).pack(pady=10)

        # Code text area
        code_text = tk.Text(
            export_window,
            font=('Courier', 10),
            bg='#1a1a1a',
            fg='lightgreen',
            wrap='word'
        )
        code_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Generate code
        code = self.generate_variation_code(variation)
        code_text.insert('1.0', code)
        code_text.config(state='disabled')

        # Copy button
        tk.Button(
            export_window,
            text="📋 Copy to Clipboard",
            command=lambda: self.copy_to_clipboard(code),
            font=('Arial', 12),
            bg='blue',
            fg='white'
        ).pack(pady=10)

    def generate_variation_code(self, variation):
        """Generate standalone code for a variation"""
        return f'''# Blood Drip Animation: {variation['name']}
import tkinter as tk
import math
import random
import time

class BloodDripSplash:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("{variation['name']}")
        self.root.geometry("300x400")
        self.root.configure(bg='black')
        self.root.overrideredirect(True)  # Remove window decorations

        # Center window
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 300) // 2
        y = (screen_height - 400) // 2
        self.root.geometry(f"300x400+{{x}}+{{y}}")

        self.canvas = tk.Canvas(self.root, width=300, height=400, bg='black', highlightthickness=0)
        self.canvas.pack()

        self.variation = {variation}
        self.animation = BloodDripAnimation(self.canvas, self.variation)

        # Auto-close after animation
        self.root.after(4000, self.root.destroy)

        self.animate()

    def animate(self):
        self.animation.animate_frame()
        self.root.after(50, self.animate)

    def show(self):
        self.root.mainloop()

# Add the BloodDripAnimation class here...
# (Use the same class from the main file)

if __name__ == "__main__":
    splash = BloodDripSplash()
    splash.show()
'''

    def copy_to_clipboard(self, text):
        """Copy text to clipboard"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)

    def run(self):
        """Run the picker application"""
        self.root.mainloop()

if __name__ == "__main__":
    picker = BloodDripPicker()
    picker.run()
