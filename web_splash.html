<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Splash Screen</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: rgba(0, 0, 0, 0.8);
            overflow: hidden;
        }

        .splash-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, 
                rgba(20, 20, 40, 0.9) 0%, 
                rgba(40, 40, 80, 0.9) 100%);
            backdrop-filter: blur(10px);
            z-index: 9999;
        }

        .splash-content {
            text-align: center;
            color: white;
            background: rgba(0, 0, 0, 0.3);
            padding: 40px;
            border-radius: 20px;
            border: 2px solid rgba(100, 150, 255, 0.3);
            box-shadow: 0 0 30px rgba(0, 150, 255, 0.2);
        }

        .logo-container {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
        }

        .spinning-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 3px solid transparent;
            border-top: 3px solid #0096ff;
            border-radius: 50%;
            animation: spin 2s linear infinite;
        }

        .spinning-ring:nth-child(2) {
            width: 80%;
            height: 80%;
            top: 10%;
            left: 10%;
            border-top-color: #00ff96;
            animation-duration: 1.5s;
            animation-direction: reverse;
        }

        .spinning-ring:nth-child(3) {
            width: 60%;
            height: 60%;
            top: 20%;
            left: 20%;
            border-top-color: #ff6b00;
            animation-duration: 1s;
        }

        .logo-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #0096ff, #00ff96);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            color: white;
            animation: pulse 2s ease-in-out infinite;
        }

        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            animation: fadeInUp 1s ease-out;
        }

        .progress-container {
            width: 250px;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            margin: 20px auto;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #0096ff, #00ff96);
            border-radius: 3px;
            width: 0%;
            animation: progressFill 4s ease-out forwards;
        }

        .status-text {
            font-size: 14px;
            color: #ccc;
            margin-top: 15px;
            animation: fadeIn 0.5s ease-in-out;
        }

        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(0, 150, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.1); }
        }

        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes progressFill {
            0% { width: 0%; }
            25% { width: 30%; }
            50% { width: 60%; }
            75% { width: 85%; }
            100% { width: 100%; }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            50% {
                transform: translateY(-100px) rotate(180deg);
            }
        }

        .fade-out {
            animation: fadeOut 1s ease-out forwards;
        }

        @keyframes fadeOut {
            0% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="splash-container" id="splashContainer">
        <div class="floating-particles" id="particles"></div>
        
        <div class="splash-content">
            <div class="logo-container">
                <div class="spinning-ring"></div>
                <div class="spinning-ring"></div>
                <div class="spinning-ring"></div>
                <div class="logo-center">APP</div>
            </div>
            
            <div class="title">Loading Application</div>
            
            <div class="progress-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <div class="status-text" id="statusText">Initializing...</div>
        </div>
    </div>

    <script>
        class SplashScreen {
            constructor() {
                this.statusMessages = [
                    "Initializing...",
                    "Loading modules...",
                    "Setting up components...",
                    "Finalizing setup...",
                    "Ready!"
                ];
                this.currentStatus = 0;
                this.progress = 0;
                
                this.init();
            }
            
            init() {
                this.createParticles();
                this.startProgressAnimation();
                this.startStatusUpdates();
                
                // Auto-hide after 4.5 seconds
                setTimeout(() => this.hideSplash(), 4500);
            }
            
            createParticles() {
                const particlesContainer = document.getElementById('particles');
                const particleCount = 20;
                
                for (let i = 0; i < particleCount; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (4 + Math.random() * 4) + 's';
                    
                    // Random colors
                    const colors = ['rgba(0, 150, 255, 0.6)', 'rgba(0, 255, 150, 0.6)', 'rgba(255, 107, 0, 0.6)'];
                    particle.style.background = colors[Math.floor(Math.random() * colors.length)];
                    
                    particlesContainer.appendChild(particle);
                }
            }
            
            startProgressAnimation() {
                const progressBar = document.getElementById('progressBar');
                let progress = 0;
                
                const interval = setInterval(() => {
                    progress += Math.random() * 3 + 1;
                    if (progress > 100) progress = 100;
                    
                    progressBar.style.width = progress + '%';
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                    }
                }, 100);
            }
            
            startStatusUpdates() {
                const statusText = document.getElementById('statusText');
                
                const updateStatus = () => {
                    if (this.currentStatus < this.statusMessages.length - 1) {
                        statusText.style.opacity = '0';
                        
                        setTimeout(() => {
                            this.currentStatus++;
                            statusText.textContent = this.statusMessages[this.currentStatus];
                            statusText.style.opacity = '1';
                        }, 300);
                        
                        setTimeout(updateStatus, 1000);
                    }
                };
                
                setTimeout(updateStatus, 800);
            }
            
            hideSplash() {
                const splashContainer = document.getElementById('splashContainer');
                splashContainer.classList.add('fade-out');
                
                setTimeout(() => {
                    splashContainer.style.display = 'none';
                    // Here you would typically show your main application
                    this.showMainApp();
                }, 1000);
            }
            
            showMainApp() {
                // Replace splash with main application content
                document.body.innerHTML = `
                    <div style="padding: 50px; text-align: center; font-family: Arial, sans-serif;">
                        <h1>Welcome to Your Application!</h1>
                        <p>The splash screen has finished loading.</p>
                        <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px; background: #0096ff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Reload Splash Screen
                        </button>
                    </div>
                `;
            }
        }
        
        // Initialize splash screen when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SplashScreen();
        });
    </script>
</body>
</html>
