# Animated Transparent Splash Screens

This project provides multiple implementations of animated splash screens with transparency support for different platforms and frameworks.

## Features

- **Multiple Implementations**: Tkinter, PyQt5, and Web-based versions
- **Transparency Support**: Semi-transparent backgrounds and alpha blending
- **Smooth Animations**: Spinning elements, pulsing logos, progress bars
- **Customizable**: Easy to modify colors, timing, and animations
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Files Overview

### 1. `animated_splash.py` - Tkinter Version
- Pure Python implementation using tkinter
- Semi-transparent window with animated elements
- Spinning circles and pulsing logo
- Progress bar with status updates
- Creates animated GIF function included

### 2. `pyqt_splash.py` - PyQt5 Version
- Advanced transparency support
- Smooth gradient backgrounds
- Multiple animation layers
- Better rendering quality
- Two versions: full-featured and simple

### 3. `web_splash.html` - Web Version
- HTML/CSS/JavaScript implementation
- CSS animations and transitions
- Floating particles effect
- Responsive design
- Perfect for web applications or Electron apps

### 4. `demo_app.py` - Demo Application
- Shows how to integrate splash screens
- Test different implementations
- Main application example

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. For PyQt5 version, you might need additional system packages:
```bash
# Ubuntu/Debian
sudo apt-get install python3-pyqt5

# macOS with Homebrew
brew install pyqt5

# Windows - pip should handle it
```

## Usage

### Quick Start
```bash
# Run the demo application
python demo_app.py

# Or run individual splash screens
python animated_splash.py
python pyqt_splash.py
```

### Web Version
Open `web_splash.html` in any modern web browser.

### Integration Examples

#### Tkinter Integration
```python
from animated_splash import AnimatedSplashScreen

# Show splash before main app
splash = AnimatedSplashScreen(duration=3000)
splash.show()

# Your main application code here
```

#### PyQt5 Integration
```python
from pyqt_splash import AnimatedSplashScreen
import sys
from PyQt5.QtWidgets import QApplication

app = QApplication(sys.argv)
splash = AnimatedSplashScreen()
splash.show()

# Load your main application
# splash will auto-close after animation

app.exec_()
```

## Customization

### Colors and Styling
- Edit color values in the respective files
- Modify gradient definitions
- Change transparency levels (alpha values)

### Animation Timing
- Adjust `duration` parameter for total splash time
- Modify timer intervals for animation speed
- Change easing functions for smoother transitions

### Content
- Replace "APP" text with your logo/brand
- Modify status messages
- Add your own animated elements

## Advanced Features

### Transparent GIF Creation
The `animated_splash.py` includes a function to create transparent animated GIFs:

```python
from animated_splash import create_transparent_gif
create_transparent_gif()  # Creates animated_logo.gif
```

### Custom Animations
Add your own animation functions:

```python
def custom_animation(self, frame):
    # Your animation logic here
    pass
```

### Multiple Screens
Support for multiple monitor setups is included - splash screens will center on the primary display.

## Browser Compatibility (Web Version)

- Chrome/Chromium: Full support
- Firefox: Full support
- Safari: Full support
- Edge: Full support
- Internet Explorer: Limited support (no backdrop-filter)

## Performance Notes

- Tkinter version: Lightweight, good for simple applications
- PyQt5 version: Best quality and performance, larger dependency
- Web version: Great for modern web apps, requires browser

## Troubleshooting

### Common Issues

1. **PyQt5 not installing**: Try `pip install PyQt5-tools` or use conda
2. **Transparency not working**: Check if your system supports compositing
3. **Animation stuttering**: Reduce timer frequency or simplify animations
4. **Web version not loading**: Ensure you're opening via HTTP/file protocol

### Platform-Specific Notes

- **Windows**: All versions work well
- **macOS**: PyQt5 version provides best transparency support
- **Linux**: Transparency depends on window manager (works well with GNOME/KDE)

## License

This project is provided as-is for educational and commercial use.

## Contributing

Feel free to submit improvements, bug fixes, or additional splash screen implementations!
