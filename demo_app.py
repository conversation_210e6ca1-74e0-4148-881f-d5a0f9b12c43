#!/usr/bin/env python3
"""
Demo application showing how to integrate animated splash screens
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Import our splash screens
from animated_splash import AnimatedSplashScreen, create_transparent_gif

class MainApplication:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Main Application")
        self.root.geometry("600x400")
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the main application UI"""
        # Title
        title_label = tk.Label(
            self.root,
            text="Main Application",
            font=('Arial', 20, 'bold')
        )
        title_label.pack(pady=20)
        
        # Description
        desc_label = tk.Label(
            self.root,
            text="This is your main application that appears after the splash screen.",
            font=('Arial', 12),
            wraplength=500
        )
        desc_label.pack(pady=10)
        
        # Buttons to test different splash screens
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=30)
        
        tk.Button(
            button_frame,
            text="Show Tkinter Splash",
            command=self.show_tkinter_splash,
            font=('Arial', 12),
            bg='lightblue',
            padx=20,
            pady=10
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            button_frame,
            text="Show PyQt Splash",
            command=self.show_pyqt_splash,
            font=('Arial', 12),
            bg='lightgreen',
            padx=20,
            pady=10
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            button_frame,
            text="Open Web Splash",
            command=self.show_web_splash,
            font=('Arial', 12),
            bg='lightyellow',
            padx=20,
            pady=10
        ).pack(side=tk.LEFT, padx=10)
        
        # Create GIF button
        tk.Button(
            self.root,
            text="Create Animated GIF",
            command=self.create_gif,
            font=('Arial', 12),
            bg='lightcoral',
            padx=20,
            pady=10
        ).pack(pady=20)
        
        # Status label
        self.status_label = tk.Label(
            self.root,
            text="Ready",
            font=('Arial', 10),
            fg='gray'
        )
        self.status_label.pack(side=tk.BOTTOM, pady=10)
    
    def show_tkinter_splash(self):
        """Show the Tkinter splash screen"""
        self.status_label.config(text="Showing Tkinter splash screen...")
        self.root.withdraw()  # Hide main window
        
        splash = AnimatedSplashScreen(duration=3000)
        splash.show()
        
        self.root.deiconify()  # Show main window again
        self.status_label.config(text="Tkinter splash screen completed")
    
    def show_pyqt_splash(self):
        """Show the PyQt splash screen"""
        try:
            self.status_label.config(text="Showing PyQt splash screen...")
            import subprocess
            subprocess.Popen([sys.executable, "pyqt_splash.py"])
            self.status_label.config(text="PyQt splash screen launched")
        except Exception as e:
            self.status_label.config(text=f"Error: {str(e)}")
    
    def show_web_splash(self):
        """Open the web splash screen"""
        try:
            import webbrowser
            file_path = os.path.abspath("web_splash.html")
            webbrowser.open(f"file://{file_path}")
            self.status_label.config(text="Web splash screen opened in browser")
        except Exception as e:
            self.status_label.config(text=f"Error: {str(e)}")
    
    def create_gif(self):
        """Create animated GIF"""
        try:
            self.status_label.config(text="Creating animated GIF...")
            create_transparent_gif()
            self.status_label.config(text="Animated GIF created: animated_logo.gif")
        except Exception as e:
            self.status_label.config(text=f"Error creating GIF: {str(e)}")
    
    def run(self):
        """Run the main application"""
        self.root.mainloop()

def main():
    """Main function with splash screen integration"""
    print("Starting application with splash screen...")
    
    # Show splash screen first
    splash = AnimatedSplashScreen(duration=2000)  # 2 seconds
    splash.show()
    
    # Then show main application
    app = MainApplication()
    app.run()

if __name__ == "__main__":
    main()
