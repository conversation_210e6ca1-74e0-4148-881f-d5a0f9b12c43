#!/usr/bin/env python3
"""
Blood Drip Splash Screen - Standalone Implementation
A dramatic blood drip animation for application splash screens
"""

import tkinter as tk
import math
import random
import time

class BloodSplashScreen:
    def __init__(self, duration=3000, variation_type='classic'):
        self.root = tk.Tk()
        self.duration = duration
        self.variation_type = variation_type
        self.setup_window()
        self.setup_animation()
        self.start_time = time.time()
        
    def setup_window(self):
        """Setup splash screen window"""
        # Remove window decorations for clean look
        self.root.overrideredirect(True)
        
        # Set window size and center it
        width, height = 300, 400
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        # Black background with transparency
        self.root.configure(bg='black')
        self.root.attributes('-alpha', 0.95)
        self.root.attributes('-topmost', True)
        
        # Create canvas for animation
        self.canvas = tk.Canvas(
            self.root,
            width=width,
            height=height,
            bg='black',
            highlightthickness=0
        )
        self.canvas.pack()
        
    def setup_animation(self):
        """Setup animation variables"""
        self.frame = 0
        self.drops = []
        self.pools = []
        self.blood_color = '#8B0000'  # Dark red
        
        # Initialize drops based on variation
        if self.variation_type == 'classic':
            self.drops.append({'x': 150, 'y': 50, 'speed': 4, 'size': 10})
        elif self.variation_type == 'multi':
            for i in range(3):
                self.drops.append({
                    'x': 120 + i * 30,
                    'y': 50 + i * 10,
                    'speed': 3 + i,
                    'size': 8 + i * 2
                })
        elif self.variation_type == 'fang':
            self.drops.append({'x': 130, 'y': 85, 'speed': 3, 'size': 8})
            self.drops.append({'x': 170, 'y': 85, 'speed': 3, 'size': 8})
    
    def animate_frame(self):
        """Animate one frame"""
        self.canvas.delete("all")
        self.frame += 1
        
        # Calculate progress
        elapsed = time.time() - self.start_time
        progress = min((elapsed * 1000) / self.duration, 1.0)
        
        # Draw based on variation type
        if self.variation_type == 'classic':
            self.draw_classic_drip()
        elif self.variation_type == 'multi':
            self.draw_multi_drip()
        elif self.variation_type == 'fang':
            self.draw_fang_drip()
        
        # Add dramatic text
        if progress > 0.3:
            alpha = min((progress - 0.3) / 0.3, 1.0)
            text_color = self.blend_red_alpha(alpha)
            
            self.canvas.create_text(
                150, 350,
                text="Loading...",
                font=('Arial', 16, 'bold'),
                fill=text_color
            )
        
        # Continue animation or close
        if progress < 1.0:
            self.root.after(50, self.animate_frame)
        else:
            self.root.after(500, self.close)
    
    def draw_classic_drip(self):
        """Draw classic single blood drip"""
        drop = self.drops[0]
        
        # Draw trail
        for i in range(8):
            trail_y = drop['y'] - i * 8
            if trail_y > 50:
                trail_size = max(2, drop['size'] - i)
                alpha = 1.0 - (i * 0.15)
                trail_color = self.blend_red_alpha(alpha)
                
                self.canvas.create_oval(
                    drop['x'] - trail_size//2, trail_y - trail_size//2,
                    drop['x'] + trail_size//2, trail_y + trail_size//2,
                    fill=trail_color, outline=''
                )
        
        # Main drop with slight teardrop shape
        self.canvas.create_oval(
            drop['x'] - drop['size']//2, drop['y'] - drop['size'],
            drop['x'] + drop['size']//2, drop['y'] + drop['size']//2,
            fill=self.blood_color, outline=''
        )
        
        # Update position
        drop['y'] += drop['speed']
        
        # Create splash when hitting bottom
        if drop['y'] > 350:
            self.create_blood_splash(drop['x'], 350)
    
    def draw_multi_drip(self):
        """Draw multiple blood drips"""
        for i, drop in enumerate(self.drops):
            # Staggered start times
            if self.frame > i * 20:
                # Draw drop
                self.canvas.create_oval(
                    drop['x'] - drop['size']//2, drop['y'] - drop['size']//2,
                    drop['x'] + drop['size']//2, drop['y'] + drop['size']//2,
                    fill=self.blood_color, outline=''
                )
                
                # Update position
                drop['y'] += drop['speed']
                
                # Create splash
                if drop['y'] > 350:
                    self.create_blood_splash(drop['x'], 350)
    
    def draw_fang_drip(self):
        """Draw blood dripping from fangs"""
        # Draw vampire fangs
        fang_positions = [(130, 60), (170, 60)]
        
        for fang_x, fang_y in fang_positions:
            # Draw fang shape
            self.canvas.create_polygon(
                fang_x, fang_y,
                fang_x - 6, fang_y + 20,
                fang_x + 6, fang_y + 20,
                fill='white', outline='lightgray'
            )
        
        # Blood drops from fang tips
        for drop in self.drops:
            if drop['y'] < 350:
                # Teardrop shape
                self.canvas.create_oval(
                    drop['x'] - drop['size']//2, drop['y'] - drop['size'],
                    drop['x'] + drop['size']//2, drop['y'] + drop['size']//2,
                    fill=self.blood_color, outline=''
                )
                
                drop['y'] += drop['speed']
            else:
                self.create_blood_splash(drop['x'], 350)
    
    def create_blood_splash(self, x, y):
        """Create blood splash effect"""
        splash_count = 12
        for i in range(splash_count):
            angle = (i * 2 * math.pi) / splash_count
            distance = random.uniform(10, 25)
            splash_x = x + distance * math.cos(angle)
            splash_y = y + distance * math.sin(angle) * 0.3  # Flatten vertically
            
            size = random.randint(2, 6)
            self.canvas.create_oval(
                splash_x - size, splash_y - size,
                splash_x + size, splash_y + size,
                fill=self.blood_color, outline=''
            )
    
    def blend_red_alpha(self, alpha):
        """Create red color with alpha blending effect"""
        if alpha <= 0:
            return '#000000'
        elif alpha < 0.3:
            return '#330000'
        elif alpha < 0.6:
            return '#660000'
        elif alpha < 0.9:
            return '#990000'
        else:
            return self.blood_color
    
    def close(self):
        """Close splash screen"""
        self.root.destroy()
    
    def show(self):
        """Show the splash screen"""
        self.animate_frame()
        self.root.mainloop()

# Preset configurations for easy use
BLOOD_VARIATIONS = {
    'classic': {'type': 'classic', 'duration': 3000},
    'multi_drip': {'type': 'multi', 'duration': 4000},
    'vampire_fangs': {'type': 'fang', 'duration': 3500},
    'quick_drip': {'type': 'classic', 'duration': 2000},
    'slow_dramatic': {'type': 'classic', 'duration': 5000}
}

def show_blood_splash(variation='classic', duration=None):
    """
    Convenience function to show blood splash screen
    
    Args:
        variation: 'classic', 'multi_drip', 'vampire_fangs', 'quick_drip', 'slow_dramatic'
        duration: Override duration in milliseconds
    """
    config = BLOOD_VARIATIONS.get(variation, BLOOD_VARIATIONS['classic'])
    
    if duration:
        config['duration'] = duration
    
    splash = BloodSplashScreen(
        duration=config['duration'],
        variation_type=config['type']
    )
    splash.show()

# Example integration with main application
class MainApp:
    def __init__(self):
        # Show blood splash first
        show_blood_splash('vampire_fangs', 3000)
        
        # Then show main application
        self.root = tk.Tk()
        self.root.title("Main Application")
        self.root.geometry("600x400")
        
        tk.Label(
            self.root,
            text="🩸 Welcome to Your Dark Application 🩸",
            font=('Arial', 20, 'bold'),
            fg='darkred'
        ).pack(pady=50)
        
        tk.Label(
            self.root,
            text="The blood drip splash screen has completed!",
            font=('Arial', 12)
        ).pack(pady=20)
        
        # Test buttons
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=30)
        
        for name, config in BLOOD_VARIATIONS.items():
            tk.Button(
                button_frame,
                text=f"Show {name.replace('_', ' ').title()}",
                command=lambda n=name: show_blood_splash(n),
                font=('Arial', 10),
                bg='darkred',
                fg='white',
                padx=10,
                pady=5
            ).pack(pady=5)
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # Demo - show different variations
    print("🩸 Blood Drip Splash Screen Demo 🩸")
    print("Choose a variation:")
    print("1. Classic Drip")
    print("2. Multi Drip") 
    print("3. Vampire Fangs")
    print("4. Quick Drip")
    print("5. Slow Dramatic")
    print("6. Run Main App with Splash")
    
    try:
        choice = input("Enter choice (1-6): ").strip()
        
        if choice == '1':
            show_blood_splash('classic')
        elif choice == '2':
            show_blood_splash('multi_drip')
        elif choice == '3':
            show_blood_splash('vampire_fangs')
        elif choice == '4':
            show_blood_splash('quick_drip')
        elif choice == '5':
            show_blood_splash('slow_dramatic')
        elif choice == '6':
            app = MainApp()
            app.run()
        else:
            # Default to classic
            show_blood_splash('classic')
            
    except (KeyboardInterrupt, EOFError):
        print("\nExiting...")
    except Exception as e:
        print(f"Error: {e}")
        # Fallback to classic
        show_blood_splash('classic')
