# 🩸 Blood Drip Splash Screen Effects

A collection of 50+ dramatic blood drip animations for application splash screens. Perfect for horror games, dark-themed applications, or any software that needs a dramatic loading effect.

## 🎯 Quick Start

### Simple Integration
```python
from blood_splash_screen import show_blood_splash

# Show before your main application
show_blood_splash('vampire_fangs', 3000)  # 3 second vampire fang drip

# Your main app code here...
```

### Available Variations
- **`classic`** - Single blood drop with trailing effect
- **`multi_drip`** - Multiple drops falling in sequence  
- **`vampire_fangs`** - Blood dripping from vampire fangs
- **`quick_drip`** - Fast 2-second blood drop
- **`slow_dramatic`** - Slow 5-second dramatic effect

## 📁 Files Overview

### 🎮 Main Files
- **`blood_splash_screen.py`** - Standalone, ready-to-use blood splash screen
- **`blood_drip_variations.py`** - Full picker app with 50 variations
- **`blood_demo.py`** - Simple demo to test different effects

### 🚀 Quick Demo
```bash
# Run the demo to test all effects
python blood_demo.py

# Run the full picker with 50 variations
python blood_drip_variations.py

# Run standalone splash screen
python blood_splash_screen.py
```

## 🎨 The 50 Variations Include:

### 🩸 **Classic Effects (1-15)**
- Different speeds (slow to fast)
- Various blood colors (dark red, crimson, burgundy)
- Multiple drop sizes (small to large)

### 🩸🩸 **Multi-Drop Effects (16-20)**
- 2-6 simultaneous drops
- Staggered timing
- Different patterns

### 💥 **Splatter Effects (21-25)**
- Blood explosion on impact
- Particle spray patterns
- Intensity variations

### 🔤 **Dripping Text (26-30)**
- Letters: V, X, A, Z, ♦
- Text that drips blood
- Custom font effects

### 💓 **Pulsing Drops (31-35)**
- Heartbeat-like pulsing
- Different pulse rates
- Growing/shrinking effects

### 🌊 **Blood Streams (36-40)**
- Flowing blood streams
- Wave motion effects
- Different widths

### 🏊 **Blood Pools (41-45)**
- Expanding blood pools
- Drip-fed pools
- Different spread rates

### ✨ **Special Effects (46-50)**
- Glowing blood drops
- Vampire fang drips
- Blood rain
- Spiral motion
- Explosive impacts

## 🛠️ Customization

### Easy Color Changes
```python
# In blood_splash_screen.py, modify:
self.blood_color = '#8B0000'  # Dark red
# Or try: '#A0001C', '#660000', '#B22222', '#DC143C'
```

### Timing Adjustments
```python
show_blood_splash('classic', duration=5000)  # 5 seconds
```

### Window Properties
```python
# Modify in BloodSplashScreen.__init__():
width, height = 300, 400  # Window size
self.root.attributes('-alpha', 0.95)  # Transparency
```

## 🎭 Integration Examples

### Horror Game Launcher
```python
from blood_splash_screen import show_blood_splash

class HorrorGameLauncher:
    def __init__(self):
        # Dramatic blood splash
        show_blood_splash('slow_dramatic', 4000)
        
        # Then show main menu
        self.show_main_menu()
```

### Dark Theme Application
```python
def startup_sequence():
    # Quick blood effect
    show_blood_splash('quick_drip', 2000)
    
    # Load application
    main_app = DarkApplication()
    main_app.run()
```

### Random Effect on Each Launch
```python
import random
from blood_splash_screen import BLOOD_VARIATIONS

def random_startup():
    effect = random.choice(list(BLOOD_VARIATIONS.keys()))
    show_blood_splash(effect)
```

## 🎨 Picker Application Features

The `blood_drip_variations.py` includes a full GUI picker with:

- **50 Different Variations** - Browse through all effects
- **Live Preview** - See animations in real-time
- **Play/Pause Controls** - Control animation playback
- **Export Function** - Generate standalone code
- **Random Selection** - Discover new effects
- **Navigation** - Previous/Next buttons and list selection

### Picker Controls
- ▶️ **Play/Pause** - Control animation
- 🔄 **Reset** - Restart current animation
- ⬅️➡️ **Previous/Next** - Navigate variations
- 🎲 **Random** - Jump to random variation
- 💾 **Export** - Generate standalone code

## 🔧 Technical Details

### Window Properties
- **Size**: 300x400 pixels (small, centered)
- **Transparency**: Semi-transparent background
- **Positioning**: Auto-centered on screen
- **Duration**: Customizable (1-10 seconds typical)
- **Framerate**: ~20 FPS for smooth animation

### Performance
- **Lightweight**: Pure tkinter, no heavy dependencies
- **Fast Loading**: Minimal startup time
- **Memory Efficient**: Small memory footprint
- **Cross-Platform**: Works on Windows, macOS, Linux

### Compatibility
- **Python 3.6+**
- **tkinter** (included with Python)
- **PIL/Pillow** (optional, for advanced effects)

## 🎪 Advanced Usage

### Custom Animation Loop
```python
class CustomBloodSplash(BloodSplashScreen):
    def animate_frame(self):
        # Your custom animation logic
        super().animate_frame()
        
        # Add custom elements
        self.canvas.create_text(150, 50, text="LOADING", fill='red')
```

### Multiple Sequential Splashes
```python
def dramatic_sequence():
    show_blood_splash('vampire_fangs', 2000)
    time.sleep(2.5)
    show_blood_splash('splatter', 1500)
    time.sleep(2)
    show_blood_splash('blood_pool', 3000)
```

### Integration with Loading Progress
```python
class LoadingWithBlood:
    def __init__(self):
        self.splash = BloodSplashScreen(duration=5000)
        self.splash.show()
        
        # Your loading logic here
        self.load_resources()
```

## 🎨 Color Variations

Try different blood colors:
- `#8B0000` - Dark Red (default)
- `#A0001C` - Crimson
- `#660000` - Dark Burgundy  
- `#B22222` - Fire Brick
- `#DC143C` - Deep Pink Red
- `#800020` - Burgundy
- `#722F37` - Dark Red Brown

## 🚨 Usage Notes

- **Auto-Close**: All splash screens close automatically
- **Non-Blocking**: Won't interfere with main application
- **Centered**: Always appears in screen center
- **Topmost**: Stays above other windows
- **Transparent**: Semi-transparent background

## 🎉 Perfect For:

- 🎮 **Horror Games** - Dramatic startup effects
- 🎭 **Dark Theme Apps** - Gothic/dark applications  
- 🎪 **Halloween Software** - Seasonal applications
- 🎨 **Creative Tools** - Art/design applications
- 🎬 **Media Players** - Video/audio players with dark themes

---

**Ready to add some drama to your application? Pick your favorite blood effect and integrate it in seconds!** 🩸
